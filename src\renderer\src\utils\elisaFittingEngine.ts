import { levenbergMarquardt } from 'ml-levenberg-marquardt';
import * as math from 'mathjs';
import { SimpleLinearRegression, ExponentialRegression, PolynomialRegression } from 'ml-regression';
import { createSpline } from 'spline-interpolator';
import { FittingOptions, FittingResult, StandardCurveData, QualityMetrics } from '@shared/types';
import logger from './logger';

export class ElisaFittingEngine {
    /**
     * 主拟合函数 - 根据模型类型选择对应的拟合方法
     */
    static fit(data: StandardCurveData, options: FittingOptions): FittingResult {
        const { concentrations, responses } = data;

        // 数据验证
        if (concentrations.length !== responses.length) {
            throw new Error('浓度和响应数据长度不匹配');
        }

        if (concentrations.length < 3) {
            throw new Error('数据点不足，至少需要3个点');
        }

        // 根据模型类型选择拟合方法
        let result: FittingResult;

        switch (options.model) {
            case 'LINEAR':
                result = this.fitLinear(concentrations, responses);
                break;
            case 'POLYNOMIAL':
                result = this.fitPolynomial(concentrations, responses, options);
                break;
            case 'LOG_LOG':
                result = this.fitLogLog(concentrations, responses);
                break;
            case 'LOGISTIC_3P':
                result = this.fitLogistic3P(concentrations, responses, options);
                break;
            case 'LOGISTIC_4P':
                result = this.fitLogistic4P(concentrations, responses, options);
                break;
            case 'LOGISTIC_5P':
                result = this.fitLogistic5P(concentrations, responses, options);
                break;
            case 'CUBIC_SPLINE':
                result = this.fitCubicSpline(concentrations, responses);
                break;
            default:
                throw new Error(`不支持的拟合模型: ${options.model}`);
        }

        // 计算质量指标
        result.qualityMetrics = this.calculateQualityMetrics(result, data);

        return result;
    }

    /**
     * 线性拟合 - 使用 ml-regression
     */
    private static fitLinear(concentrations: number[], responses: number[]): FittingResult {
        try {
            // 创建回归模型
            const regression = new SimpleLinearRegression(concentrations, responses);

            // 获取模型参数
            const slope = regression.slope;
            const intercept = regression.intercept;

            // 预测函数
            const predict = (x: number) => regression.predict(x);
            const predictConcentration = (y: number) => (y - intercept) / slope;

            // 计算预测值
            const predicted = concentrations.map(predict);

            // Fix the rSquared calculation for linear regression
            const scoreResult = regression.score(concentrations, responses);
            const rSquared =
                typeof scoreResult === 'object' && scoreResult.r2 !== undefined
                    ? scoreResult.r2
                    : scoreResult;

            return {
                model: 'LINEAR',
                parameters: [regression.slope, regression.intercept],
                rSquared: rSquared,
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: `y = ${slope.toFixed(4)}x + ${intercept.toFixed(4)}`,
                convergence: true,
                iterations: 1,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('线性拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                '线性拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * 多项式拟合 - 使用 ml-regression
     */
    private static fitPolynomial(
        concentrations: number[],
        responses: number[],
        options: FittingOptions
    ): FittingResult {
        try {
            const degree = options.polynomialDegree || 2; // 默认二次多项式
            const regression = new PolynomialRegression(concentrations, responses, degree);

            // 预测函数
            const predict = (x: number) => regression.predict(x);

            // 浓度预测（使用牛顿迭代法）
            const predictConcentration = (y: number) => {
                const maxIterations = 50;
                const tolerance = 1e-6;
                let x = math.mean(concentrations) as number; // 初始猜测值

                for (let i = 0; i < maxIterations; i++) {
                    const y_pred = predict(x);
                    const error = y_pred - y;

                    if (Math.abs(error) < tolerance) {
                        return x;
                    }

                    // 数值微分计算导数
                    const h = 1e-6;
                    const derivative = (predict(x + h) - predict(x)) / h;

                    // 防止除以零
                    if (Math.abs(derivative) < 1e-10) {
                        break;
                    }

                    // 牛顿迭代步进
                    x = x - error / derivative;
                }

                // 验证结果是否在有效范围内
                const minX = Math.min(...concentrations);
                const maxX = Math.max(...concentrations);
                return x >= minX && x <= maxX ? x : NaN;
            };

            // 计算预测值
            const predicted = concentrations.map(predict);

            // 构建方程字符串
            const coefficients = regression.coefficients;
            const equation = coefficients
                .map((coef, i) => {
                    if (i === 0) return coef.toFixed(4);
                    if (i === 1) return `${coef >= 0 ? '+' : ''}${coef.toFixed(4)}x`;
                    return `${coef >= 0 ? '+' : ''}${coef.toFixed(4)}x^${i}`;
                })
                .join('');

            // Fix polynomial regression rSquared
            const rSquaredValue = regression.score(concentrations, responses);

            return {
                model: 'POLYNOMIAL',
                parameters: coefficients,
                rSquared: rSquaredValue,
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: `y = ${equation}`,
                convergence: true,
                iterations: 1,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('多项式拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                '多项式拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * 双对数拟合 - 使用 ml-regression
     */
    private static fitLogLog(concentrations: number[], responses: number[]): FittingResult {
        try {
            // 创建指数回归模型
            const regression = new ExponentialRegression(
                concentrations.map((x) => Math.log10(x)),
                responses.map((y) => Math.log10(y))
            );

            // 获取模型参数
            const [slope, intercept] = [regression.slope, regression.intercept];

            // 预测函数
            const predict = (x: number) => Math.pow(10, regression.predict(Math.log10(x)));
            const predictConcentration = (y: number) =>
                Math.pow(10, (Math.log10(y) - intercept) / slope);

            // 计算预测值
            const predicted = concentrations.map(predict);

            return {
                model: 'LOG_LOG',
                parameters: [slope, intercept],
                rSquared: regression.score(
                    concentrations.map((x) => Math.log10(x)),
                    responses.map((y) => Math.log10(y))
                ),
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: `log(y) = ${slope.toFixed(4)}log(x) + ${intercept.toFixed(4)}`,
                convergence: true,
                iterations: 1,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('双对数拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                '双对数拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * Logistic 3P拟合 使用ml-levenberg-marquardt
     */
    private static fitLogistic3P(
        concentrations: number[],
        responses: number[],
        options: FittingOptions
    ): FittingResult {
        const data = { x: concentrations, y: responses };
        const maxY = math.max(responses) as number;
        const midX = math.median(concentrations) as number;

        const lmOptions = {
            damping: options.damping || 1e-3,
            maxIterations: options.maxIterations || 1000,
            errorTolerance: options.errorTolerance || 1e-8,
            initialValues: [maxY, -1, midX]
        };

        const threePLFunction = (params: number[]) => (x: number) => {
            const [a, b, c] = params;
            return a / (1 + (math.pow(x / c, b) as number));
        };

        try {
            const result = levenbergMarquardt(data, threePLFunction, lmOptions);
            const [a, b, c] = result.parameterValues;

            const predict = (x: number) => threePLFunction(result.parameterValues)(x);
            const predictConcentration = (y: number) => {
                if (y <= 0 || y >= a) return NaN;
                return c * Math.pow(a / y - 1, 1 / b);
            };

            const predicted = concentrations.map(predict);

            return {
                model: 'LOGISTIC_3P',
                parameters: result.parameterValues,
                rSquared: this.calculateRSquared(responses, predicted),
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: `y = ${a.toFixed(4)} / (1 + (x / ${c.toFixed(4)})^${b.toFixed(4)})`,
                convergence: result.iterations < options.maxIterations!,
                iterations: result.iterations || 0,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('Logistic 3P拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                'Logistic 3P拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * Logistic 4P拟合 使用ml-levenberg-marquardt
     */
    private static fitLogistic4P(
        concentrations: number[],
        responses: number[],
        options: FittingOptions
    ): FittingResult {
        const data = { x: concentrations, y: responses };

        // 初始参数估计
        const minY = math.min(responses) as number;
        const maxY = math.max(responses) as number;
        const midX = math.median(concentrations) as number;

        const lmOptions = {
            damping: options.damping || 1e-3,
            maxIterations: options.maxIterations || 1000,
            errorTolerance: options.errorTolerance || 1e-8,
            initialValues: [minY, maxY, midX, -1]
        };

        const fourPLFunction = (params: number[]) => (x: number) => {
            const [a, d, c, b] = params;
            return a + (d - a) / (1 + Math.pow(x / c, b));
        };

        try {
            const result = levenbergMarquardt(data, fourPLFunction, lmOptions);
            const [a, d, c, b] = result.parameterValues;

            const predict = (x: number) => fourPLFunction(result.parameterValues)(x);
            const predictConcentration = (y: number) => {
                if (y <= a || y >= d) return NaN;
                return c * Math.pow((d - a) / (y - a) - 1, 1 / b);
            };

            const predicted = concentrations.map(predict);

            return {
                model: 'LOGISTIC_4P',
                parameters: result.parameterValues,
                rSquared: this.calculateRSquared(responses, predicted),
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: `y = ${a.toFixed(4)} + (${d.toFixed(4)} - ${a.toFixed(4)}) / (1 + (x / ${c.toFixed(4)})^${b.toFixed(4)})`,
                convergence: result.iterations < options.maxIterations!,
                iterations: result.iterations || 0,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('Logistic 4P拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                'Logistic 4P拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * Logistic 5P拟合 使用ml-levenberg-marquardt
     */
    private static fitLogistic5P(
        concentrations: number[],
        responses: number[],
        options: FittingOptions
    ): FittingResult {
        const data = { x: concentrations, y: responses };

        const minY = math.min(responses) as number;
        const maxY = math.max(responses) as number;
        const midX = math.median(concentrations) as number;

        const lmOptions = {
            damping: options.damping || 1e-3,
            maxIterations: options.maxIterations || 1000,
            errorTolerance: options.errorTolerance || 1e-8,
            initialValues: [minY, maxY, midX, -1, 1]
        };

        const fivePLFunction = (params: number[]) => (x: number) => {
            const [a, d, c, b, g] = params;
            return a + (d - a) / Math.pow(1 + Math.pow(x / c, b), g);
        };

        try {
            const result = levenbergMarquardt(data, fivePLFunction, lmOptions);
            const [a, d, c, b, g] = result.parameterValues;

            const predict = (x: number) => fivePLFunction(result.parameterValues)(x);
            const predictConcentration = (y: number) => {
                if (y <= a || y >= d) return NaN;
                return c * Math.pow(Math.pow((d - a) / (y - a), 1 / g) - 1, 1 / b);
            };

            const predicted = concentrations.map(predict);

            return {
                model: 'LOGISTIC_5P',
                parameters: result.parameterValues,
                rSquared: this.calculateRSquared(responses, predicted),
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: `y = ${a.toFixed(4)} + (${d.toFixed(4)} - ${a.toFixed(4)}) / (1 + (x / ${c.toFixed(4)})^${b.toFixed(4)})^${g.toFixed(4)}`,
                convergence: result.iterations < options.maxIterations!,
                iterations: result.iterations || 0,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('Logistic 5P拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                'Logistic 5P拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * 三次样条拟合 - 使用 spline-interpolator
     */
    private static fitCubicSpline(concentrations: number[], responses: number[]): FittingResult {
        try {
            // 创建样条插值器
            const spline = createSpline({
                points: concentrations.map((x, i) => [x, responses[i]]),
                degree: 3, // 三次样条
                tension: 0.5 // 平滑度
            });

            // 预测函数
            const predict = (x: number) => {
                try {
                    return spline.at(x)[1]; // [1]是y值
                } catch {
                    return NaN;
                }
            };

            // 浓度预测（通过二分查找）
            const predictConcentration = (y: number) => {
                const tolerance = 1e-6;
                const maxIterations = 50;

                // 检查是否在范围内
                const minX = Math.min(...concentrations);
                const maxX = Math.max(...concentrations);
                const minY = predict(minX);
                const maxY = predict(maxX);

                if (y < Math.min(minY, maxY) || y > Math.max(minY, maxY)) {
                    return NaN;
                }

                // 二分查找
                let left = minX;
                let right = maxX;
                let iterations = 0;

                while (iterations < maxIterations && right - left > tolerance) {
                    const mid = (left + right) / 2;
                    const midY = predict(mid);

                    if (Math.abs(midY - y) < tolerance) {
                        return mid;
                    }

                    if (midY < y) {
                        left = mid;
                    } else {
                        right = mid;
                    }

                    iterations++;
                }

                return (left + right) / 2;
            };

            // 计算预测值和R²
            const predicted = concentrations.map(predict);
            const rSquared = this.calculateRSquared(responses, predicted);

            return {
                model: 'CUBIC_SPLINE',
                parameters: [], // 样条插值没有固定参数
                rSquared,
                residuals: this.calculateResiduals(responses, predicted),
                predict,
                predictConcentration,
                equation: '三次样条插值',
                convergence: true,
                iterations: 1,
                qualityMetrics: null
            };
        } catch (error) {
            logger.error('三次样条拟合失败', error, {
                component: './src/renderer/src/utils/elisaFittingEngine.ts'
            });
            throw new Error(
                '三次样条拟合失败: ' + (error instanceof Error ? error.message : '未知错误')
            );
        }
    }

    /**
     * 计算R²值
     */
    private static calculateRSquared(observed: number[], predicted: number[]): number {
        const meanObserved = math.mean(observed) as number;
        const totalSumSquares = math.sum(
            observed.map((y) => Math.pow(y - meanObserved, 2))
        ) as number;
        const residualSumSquares = math.sum(
            observed.map((y, i) => Math.pow(y - predicted[i], 2))
        ) as number;

        return 1 - residualSumSquares / totalSumSquares;
    }

    /**
     * 计算残差
     */
    private static calculateResiduals(observed: number[], predicted: number[]): number[] {
        return observed.map((y, i) => y - predicted[i]);
    }

    /**
     * 计算质量指标
     */
    private static calculateQualityMetrics(
        result: FittingResult,
        data: StandardCurveData
    ): QualityMetrics {
        const warnings: string[] = [];

        // R²检查
        if (result.rSquared < 0.95) {
            warnings.push(`R²值较低 (${result.rSquared.toFixed(4)})`);
        }

        // 收敛性检查
        if (!result.convergence) {
            warnings.push('拟合未收敛');
        }

        // 残差检查
        const maxResidual = math.max(result.residuals.map(Math.abs)) as number;
        const meanResponse = math.mean(data.responses) as number;
        if (maxResidual > meanResponse * 0.1) {
            warnings.push('存在较大残差');
        }

        return {
            isValid: result.rSquared > 0.9 && result.convergence,
            warnings,
            rSquared: result.rSquared,
            maxResidual,
            convergence: result.convergence
        };
    }
}
