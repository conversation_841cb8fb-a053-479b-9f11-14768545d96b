const enUS = {
    // Common translations
    common: {
        company: "Kehua (Xi'an) Bio-engineering Co., Ltd.",
        appName: 'KHB ELISA ST Series Instrument Control Software',
        about: 'About...',

        mainWindow: {
            tabs: {
                home: 'Home',
                elisaControl: 'ELISA Control',
                resultData: 'Result Data'
            }
        },
        commonDefines: {
            sampleType: {
                normal: 'Normal Sample',
                blank: 'Blank',
                negativeControl: 'Negative Control',
                weakPositive: 'Weak Positive',
                positiveControl: 'Positive Control',
                standard: 'Standard',
                qualityControl: 'Quality Control'
            },
            deviceNames: {
                unknown: 'Unknown Device',
                st360: 'ST-360 ELISA Reader',
                st960: 'ST-960 ELISA Reader'
            },
            deviceTypes: {
                unknown: 'Unknown Device',
                reader: 'ELISA Reader'
            }
        },
        button: {
            entry: 'Enter...',
            save: 'Save',
            cancel: 'Cancel',
            confirm: 'Confirm',
            delete: 'Delete',
            edit: 'Edit',
            add: 'Add',
            start: 'Start',
            stop: 'Stop',
            reset: 'Reset',
            next: 'Next',
            previous: 'Previous',
            finish: 'Finish',
            close: 'Close'
        },
        label: {
            version: 'Version:'
        },
        message: {
            saveSuccess: 'Successfully saved',
            deleteSuccess: 'Successfully deleted',
            updateSuccess: 'Successfully updated',
            createSuccess: 'Successfully created',
            uploadSuccess: 'Successfully uploaded',
            downloadSuccess: 'Successfully downloaded',
            importSuccess: 'Successfully imported',
            exportSuccess: 'Successfully exported',

            saveFailed: 'Save failed',
            deleteFailed: 'Delete failed',
            updateFailed: 'Update failed',
            createFailed: 'Create failed',
            uploadFailed: 'Upload failed',
            downloadFailed: 'Download failed',
            importFailed: 'Import failed',
            exportFailed: 'Export failed',

            confirm: {
                delete: 'Are you sure you want to delete?',
                cancel: 'Are you sure you want to cancel?',
                discard: 'Are you sure you want to discard changes?'
            },
            loading: {
                saving: 'Saving...',
                loading: 'Loading...',
                processing: 'Processing...',
                uploading: 'Uploading...',
                downloading: 'Downloading...'
            }
        },
        form: {
            required: 'This field is required',
            invalid_format: 'Invalid format',
            min_length: 'Minimum length is {{length}} characters',
            max_length: 'Maximum length is {{length}} characters',
            min_value: 'Minimum value is {{value}}',
            max_value: 'Maximum value is {{value}}',
            invalid_email: 'Invalid email address',
            invalid_date: 'Invalid date format',
            invalid_time: 'Invalid time format',
            invalid_number: 'Invalid number format',
            passwords_not_match: 'Passwords do not match',
            select_option: 'Please select an option',
            file_required: 'Please select a file',
            file_type_error: 'Invalid file type',
            file_size_error: 'File size exceeds limit'
        }
    },

    // Component translations
    components: {
        languageSwitch: {
            toast: {
                title: 'Change language',
                success: {
                    description: 'language switch success.'
                },
                error: {
                    description: 'language switch failed, please try again later.'
                }
            }
        },
        detectionInfoForm: {
            title: 'Detection Information',
            sampleType: 'Sample Type',
            projectName: 'Project Name',
            testDate: 'Test Date',
            operator: 'Operator',
            remarks: 'Remarks'
        },
        elisaPlateForm: {
            title: 'ELISA Plate Layout',
            plate: {
                rows: 'Rows (A-H)',
                columns: 'Columns (1-12)',
                well: 'Well {{position}}'
            },
            well_types: {
                title: 'Well Types',
                standard: 'Standard',
                sample: 'Sample',
                blank: 'Blank',
                control: {
                    positive: 'Positive Control',
                    negative: 'Negative Control',
                    quality: 'Quality Control'
                },
                empty: 'Empty'
            },
            settings: {
                replicates: {
                    label: 'Replicates',
                    help: 'Number of replicate wells per sample'
                },
                direction: {
                    label: 'Fill Direction',
                    horizontal: 'Horizontal',
                    vertical: 'Vertical'
                },
                start_position: {
                    label: 'Start Position',
                    placeholder: 'Select start well'
                }
            },
            concentration: {
                label: 'Concentration',
                unit: 'ng/mL',
                dilution: {
                    label: 'Dilution Factor',
                    auto: 'Auto Calculate'
                }
            },
            sample: {
                id: 'Sample ID',
                name: 'Sample Name',
                type: 'Sample Type',
                dilution: 'Dilution'
            },
            actions: {
                clear: 'Clear Layout',
                auto_fill: 'Auto Fill',
                copy: 'Copy Layout',
                paste: 'Paste Layout'
            },
            validation: {
                required_wells: 'Please assign all required wells',
                standards_required: 'Standard wells are required for quantitative analysis',
                controls_required: 'Control wells are required',
                invalid_replicates: 'Invalid number of replicates',
                duplicate_position: 'Duplicate well position'
            }
        },
        sampleTypeForm: {
            title: 'Sample Type Settings',
            fields: {
                type: {
                    label: 'Sample Type',
                    placeholder: 'Select sample type',
                    options: {
                        serum: 'Serum',
                        plasma: 'Plasma',
                        whole_blood: 'Whole Blood',
                        urine: 'Urine',
                        other: 'Other'
                    }
                },
                dilution: {
                    label: 'Sample Dilution',
                    ratio: 'Dilution Ratio',
                    custom: 'Custom Dilution'
                },
                volume: {
                    label: 'Sample Volume (μL)',
                    placeholder: 'Enter sample volume'
                },
                preparation: {
                    label: 'Sample Preparation',
                    notes: 'Preparation Notes',
                    placeholder: 'Enter preparation instructions'
                }
            },
            validation: {
                type_required: 'Sample type is required',
                volume_required: 'Sample volume is required',
                volume_range: 'Volume must be between {{min}} and {{max}} μL',
                dilution_required: 'Dilution ratio is required',
                dilution_format: 'Invalid dilution format'
            }
        },
        templateModal: {
            title: {
                new: 'New Template',
                edit: 'Edit Template',
                save: 'Save Template',
                load: 'Load Template'
            },
            fields: {
                name: {
                    label: 'Template Name',
                    placeholder: 'Enter template name'
                },
                description: {
                    label: 'Description',
                    placeholder: 'Enter template description'
                },
                category: {
                    label: 'Category',
                    placeholder: 'Select template category'
                },
                type: {
                    label: 'Detection Type',
                    options: {
                        qualitative: 'Qualitative',
                        quantitative: 'Quantitative',
                        semi_quantitative: 'Semi-quantitative'
                    }
                }
            },
            list: {
                title: 'Template List',
                empty: 'No templates found',
                search: 'Search templates',
                filter: 'Filter by category'
            },
            actions: {
                create: 'Create Template',
                save: 'Save Template',
                delete: 'Delete Template',
                load: 'Load Template',
                cancel: 'Cancel'
            },
            messages: {
                save_success: 'Template saved successfully',
                save_error: 'Failed to save template',
                delete_confirm: 'Are you sure you want to delete this template?',
                delete_success: 'Template deleted successfully',
                delete_error: 'Failed to delete template',
                load_error: 'Failed to load template'
            },
            validation: {
                name_required: 'Template name is required',
                name_exists: 'Template name already exists',
                category_required: 'Category is required',
                type_required: 'Detection type is required'
            }
        },
        detectionForm: {
            title: 'Detection Settings',
            method: 'Detection Method',
            wavelength: 'Wavelength',
            time: 'Detection Time'
        },
        infoCard: {
            device: 'Device Information',
            project: 'Project Information',
            status: 'Status',
            serialNumber: 'Serial Number'
        },
        projectForm: {
            title: 'Project Information',
            fields: {
                project_name: {
                    label: 'Project Name',
                    placeholder: 'Enter project name'
                },
                project_code: {
                    label: 'Project Code',
                    placeholder: 'Enter project code'
                },
                description: {
                    label: 'Description',
                    placeholder: 'Enter project description'
                },
                operator: {
                    label: 'Operator',
                    placeholder: 'Enter operator name'
                },
                date: {
                    label: 'Test Date',
                    placeholder: 'Select test date'
                },
                department: {
                    label: 'Department',
                    placeholder: 'Select department'
                }
            },
            validation: {
                project_name: {
                    required: 'Project name is required',
                    min_length: 'Project name must be at least {{min}} characters',
                    max_length: 'Project name cannot exceed {{max}} characters'
                },
                project_code: {
                    required: 'Project code is required',
                    format: 'Invalid project code format'
                },
                operator: {
                    required: 'Operator name is required'
                },
                date: {
                    required: 'Test date is required',
                    invalid: 'Invalid date'
                }
            }
        },
        ReportPara: {
            title: 'Report Para.',

            reportMainTitile: 'Main Title',
            reportMainTitile_placeholder: 'Enter report main title',
            reportSubTitile: 'Sub Title',
            reportSubTitile_placeholder: 'Enter report sub title',
            testMethod: 'Test Method',
            testMethod_placeholder: 'Enter test method',
            testBasis: 'Test Basis',
            testBasis_placeholder: 'Enter test basis',
            testOperator: 'Test Operator',
            testOperator_placeholder: 'Enter test operator name',
            reviewer: 'Reviewer',
            reviewer_placeholder: 'Enter reviewer name',
            toast: {
                success: {
                    title: 'Save Successful',
                    description: 'Report parameters have been updated'
                },
                error: {
                    title: 'Save Failed',
                    description: 'Error occurred while saving report parameters'
                }
            }
        },
        reagentSupplier: {
            title: 'Reagent Supplier',
            placeholder: 'Enter supplier name',
            toast: {
                success: {
                    title: 'Save Successful',
                    description: 'Supplier list has been updated'
                },
                error: {
                    title: 'Save Failed',
                    description: 'Error occurred while saving supplier list'
                }
            }
        },
        lisPara: {
            title: 'LIS Para.',
            defaultPath: 'LIS File Default Save Path',
            selectPath: 'Select LIS Save Path',
            placeholder: 'Please select LIS file save path',
            toast: {
                success: {
                    title: 'Save Successful',
                    description: 'LIS file save path has been updated'
                },
                error: {
                    title: 'Save Failed',
                    description: 'Error occurred while saving LIS path'
                },
                noSelection: {
                    title: 'No Path Selected',
                    description: 'Please select a path for LIS file saving'
                }
            }
        }
    },

    // Page translations
    pages: {
        home: {
            infoCard: {
                device: {
                    title: 'Device Management',
                    description:
                        'Default connection device management, device connection parameter configuration...'
                },
                project: {
                    title: 'Project Management',
                    description:
                        'Detection project management (add, modify, delete), detection parameter configuration, data analysis method editing...'
                },
                report: {
                    title: 'Report Management',
                    description: 'Customize detection report, edit print information, export LIS...'
                }
            }
        },
        elisaControl: {
            title: 'ELISA Control',
            plate_setup: {
                title: 'Plate Setup',
                template: {
                    label: 'Plate Template',
                    new: 'New Template',
                    load: 'Load Template',
                    save: 'Save Template'
                },
                well_type: {
                    label: 'Well Type',
                    standard: 'Standard',
                    sample: 'Sample',
                    blank: 'Blank',
                    control: 'Control'
                },
                concentration: {
                    label: 'Concentration',
                    unit: 'ng/mL'
                }
            },
            detection: {
                title: 'Detection Control',
                settings: {
                    wavelength: 'Wavelength',
                    temperature: 'Temperature',
                    shaking: 'Shaking',
                    reading_mode: 'Reading Mode'
                },
                progress: {
                    preparing: 'Preparing...',
                    detecting: 'Detecting...',
                    processing: 'Processing...',
                    completed: 'Completed',
                    remaining_time: 'Remaining Time: {{time}}'
                },
                actions: {
                    start: 'Start Detection',
                    pause: 'Pause',
                    resume: 'Resume',
                    stop: 'Stop',
                    reset: 'Reset'
                }
            },
            results: {
                title: 'Real-time Results',
                raw_data: 'Raw Data',
                processed_data: 'Processed Data',
                standard_curve: 'Standard Curve',
                export: {
                    raw: 'Export Raw Data',
                    processed: 'Export Processed Data',
                    report: 'Generate Report'
                }
            },
            status: {
                device: {
                    title: 'Device Status',
                    temperature: 'Temperature: {{value}}°C',
                    lamp: 'Lamp: {{status}}',
                    door: 'Door: {{status}}'
                },
                detection: {
                    title: 'Detection Status',
                    current_well: 'Current Well: {{position}}',
                    progress: 'Progress: {{value}}%',
                    time_elapsed: 'Time Elapsed: {{time}}'
                }
            }
        },
        resultData: {
            title: 'Detection Results',
            filter: {
                title: 'Filter Results',
                date_range: {
                    label: 'Date Range',
                    start: 'Start Date',
                    end: 'End Date'
                },
                project: {
                    label: 'Project',
                    placeholder: 'Select project'
                },
                operator: {
                    label: 'Operator',
                    placeholder: 'Select operator'
                },
                status: {
                    label: 'Status',
                    all: 'All',
                    completed: 'Completed',
                    processing: 'Processing',
                    error: 'Error'
                }
            },
            table: {
                columns: {
                    id: 'ID',
                    project_name: 'Project Name',
                    date: 'Test Date',
                    operator: 'Operator',
                    status: 'Status',
                    result: 'Result',
                    actions: 'Actions'
                },
                empty: 'No results found',
                loading: 'Loading results...'
            },
            details: {
                title: 'Result Details',
                export: 'Export Results',
                print: 'Print Report',
                delete: 'Delete Result'
            },
            chart: {
                title: 'Result Analysis',
                concentration: 'Concentration',
                absorbance: 'Absorbance',
                standard_curve: 'Standard Curve',
                sample_points: 'Sample Points'
            }
        },
        deviceManager: {
            title: 'Device Manager',
            connection: {
                title: 'Connection Settings',
                port: {
                    label: 'Serial Port',
                    placeholder: 'Select serial port'
                },
                baudRate: {
                    label: 'Baud Rate',
                    placeholder: 'Select baud rate'
                },
                status: {
                    connected: 'Connected',
                    disconnected: 'Disconnected',
                    connecting: 'Connecting...',
                    error: 'Connection Error'
                },
                buttons: {
                    connect: 'Connect',
                    disconnect: 'Disconnect',
                    refresh: 'Refresh Ports'
                }
            },
            control: {
                title: 'Device Control',
                temperature: {
                    label: 'Temperature Control',
                    current: 'Current: {{value}}°C',
                    target: 'Target: {{value}}°C',
                    set: 'Set Temperature'
                },
                motor: {
                    label: 'Motor Control',
                    position: 'Position: {{value}}',
                    speed: 'Speed: {{value}}',
                    home: 'Home Position',
                    move: 'Move To Position'
                },
                lamp: {
                    label: 'Lamp Control',
                    on: 'Turn On',
                    off: 'Turn Off',
                    status: 'Status: {{status}}'
                }
            },
            calibration: {
                title: 'Device Calibration',
                wavelength: {
                    label: 'Wavelength Calibration',
                    start: 'Start Calibration',
                    status: 'Status: {{status}}'
                },
                position: {
                    label: 'Position Calibration',
                    start: 'Start Calibration',
                    status: 'Status: {{status}}'
                }
            },
            maintenance: {
                title: 'Maintenance',
                last_calibration: 'Last Calibration: {{date}}',
                next_calibration: 'Next Calibration: {{date}}',
                lamp_hours: 'Lamp Hours: {{hours}}',
                system_check: 'System Check',
                self_test: 'Self Test'
            }
        },
        projectManager: {
            title: 'Project Manager',
            search_placeholder: 'Search [Project Name/Code]',
            isLoadingProjects: 'Loading projects...',
            emptyProjects: 'No matching projects found',
            label_deletePro: 'Delete Project',
            label_addPro: 'Add Project',
            type: 'Type',
            testWave: 'TestWave',
            refWave: 'RefWave',
            proPara: 'Project Para.',

            para: {
                title: 'Report Para.'
            },
            supplier: {
                title: 'Reagent Supplier'
            },
            lis: {
                title: 'LIS Para.'
            }
        },
        ReportManager: {}
    }
} as const;

export default enUS;
