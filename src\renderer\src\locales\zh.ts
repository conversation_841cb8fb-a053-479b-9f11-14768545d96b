const zhCN = {
    // 通用翻译
    common: {
        company: '科华（西安）生物工程有限公司',
        appName: '科华酶免ST系列系统软件',
        about: '关于...',

        mainWindow: {
            tabs: {
                home: '主页',
                elisaControl: '酶标仪控制',
                resultData: '结果数据'
            }
        },
        commonDefines: {
            sampleType: {
                normal: '普通标本',
                blank: '空白',
                negativeControl: '阴性对照',
                weakPositive: '弱阳性',
                positiveControl: '阳性对照',
                standard: '标准品',
                qualityControl: '质控品'
            },
            deviceNames: {
                unknown: '未知设备',
                st360: 'ST-360 酶标仪',
                st960: 'ST-960 酶标仪'
            },
            deviceTypes: {
                unknown: '未知设备',
                reader: '酶标仪'
            }
        },
        button: {
            entry: '进入...',
            save: '保存',
            cancel: '取消',
            confirm: '确认',
            delete: '删除',
            edit: '编辑',
            add: '添加',
            start: '开始',
            stop: '停止',
            reset: '重置',
            next: '下一步',
            previous: '上一步',
            finish: '完成',
            close: '关闭'
        },
        label: {
            version: '版本号：'
        },
        message: {
            saveSuccess: '保存成功',
            deleteSuccess: '删除成功',
            updateSuccess: '更新成功',
            createSuccess: '创建成功',
            uploadSuccess: '上传成功',
            downloadSuccess: '下载成功',
            importSuccess: '导入成功',
            exportSuccess: '导出成功',

            saveFailed: '保存失败',
            deleteFailed: '删除失败',
            updateFailed: '更新失败',
            createFailed: '创建失败',

            uploadFailed: '上传失败',
            downloadFailed: '下载失败',
            importFailed: '导入失败',
            exportFailed: '导出失败',

            confirm: {
                delete: '确定要删除吗？',
                cancel: '确定要取消吗？',
                discard: '确定要放弃更改吗？'
            },
            loading: {
                saving: '保存中...',
                loading: '加载中...',
                processing: '处理中...',
                uploading: '上传中...',
                downloading: '下载中...'
            }
        },
        form: {
            required: '此字段为必填项',
            invalid_format: '格式无效',
            min_length: '最小长度为 {{length}} 个字符',
            max_length: '最大长度为 {{length}} 个字符',
            min_value: '最小值为 {{value}}',
            max_value: '最大值为 {{value}}',
            invalid_email: '邮箱地址无效',
            invalid_date: '日期格式无效',
            invalid_time: '时间格式无效',
            invalid_number: '数字格式无效',
            passwords_not_match: '密码不匹配',
            select_option: '请选择一个选项',
            file_required: '请选择文件',
            file_type_error: '文件类型无效',
            file_size_error: '文件大小超出限制'
        }
    },

    // 组件翻译
    components: {
        languageSwitch: {
            toast: {
                title: '语言切换',
                success: {
                    description: '语言切换成功'
                },
                error: {
                    description: '语言切换失败,请稍后重试'
                }
            }
        },
        detectionInfoForm: {
            title: '检测信息',
            sampleType: '样本类型',
            projectName: '项目名称',
            testDate: '检测日期',
            operator: '操作员',
            remarks: '备注'
        },
        elisaPlateForm: {
            title: '酶标板布局',
            plate: {
                rows: '行 (A-H)',
                columns: '列 (1-12)',
                well: '孔位 {{position}}'
            },
            well_types: {
                title: '孔位类型',
                standard: '标准品',
                sample: '样本',
                blank: '空白',
                control: {
                    positive: '阳性对照',
                    negative: '阴性对照',
                    quality: '质控品'
                },
                empty: '空'
            },
            settings: {
                replicates: {
                    label: '重复数',
                    help: '每个样本的重复孔数'
                },
                direction: {
                    label: '填充方向',
                    horizontal: '水平',
                    vertical: '垂直'
                },
                start_position: {
                    label: '起始位置',
                    placeholder: '选择起始孔位'
                }
            },
            concentration: {
                label: '浓度',
                unit: 'ng/mL',
                dilution: {
                    label: '稀释倍数',
                    auto: '自动计算'
                }
            },
            sample: {
                id: '样本编号',
                name: '样本名称',
                type: '样本类型',
                dilution: '稀释度'
            },
            actions: {
                clear: '清空布局',
                auto_fill: '自动填充',
                copy: '复制布局',
                paste: '粘贴布局'
            },
            validation: {
                required_wells: '请分配所有必需的孔位',
                standards_required: '定量分析需要标准品孔',
                controls_required: '需要对照孔',
                invalid_replicates: '无效的重复数',
                duplicate_position: '重复的孔位'
            }
        },
        sampleTypeForm: {
            title: '样本类型设置',
            fields: {
                type: {
                    label: '样本类型',
                    placeholder: '请选择样本类型',
                    options: {
                        serum: '血清',
                        plasma: '血浆',
                        whole_blood: '全血',
                        urine: '尿液',
                        other: '其他'
                    }
                },
                dilution: {
                    label: '样本稀释',
                    ratio: '稀释比例',
                    custom: '自定义稀释'
                },
                volume: {
                    label: '样本体积 (μL)',
                    placeholder: '请输入样本体积'
                },
                preparation: {
                    label: '样本制备',
                    notes: '制备说明',
                    placeholder: '请输入制备说明'
                }
            },
            validation: {
                type_required: '样本类型为必填项',
                volume_required: '样本体积为必填项',
                volume_range: '体积必须在 {{min}} 和 {{max}} μL 之间',
                dilution_required: '稀释比例为必填项',
                dilution_format: '无效的稀释格式'
            }
        },
        templateModal: {
            title: {
                new: '新建模板',
                edit: '编辑模板',
                save: '保存模板',
                load: '加载模板'
            },
            fields: {
                name: {
                    label: '模板名称',
                    placeholder: '请输入模板名称'
                },
                description: {
                    label: '描述',
                    placeholder: '请输入模板描述'
                },
                category: {
                    label: '分类',
                    placeholder: '请选择模板分类'
                },
                type: {
                    label: '检测类型',
                    options: {
                        qualitative: '定性',
                        quantitative: '定量',
                        semi_quantitative: '半定量'
                    }
                }
            },
            list: {
                title: '模板列表',
                empty: '暂无模板',
                search: '搜索模板',
                filter: '按分类筛选'
            },
            actions: {
                create: '创建模板',
                save: '保存模板',
                delete: '删除模板',
                load: '加载模板',
                cancel: '取消'
            },
            messages: {
                save_success: '模板保存成功',
                save_error: '模板保存失败',
                delete_confirm: '确定要删除此模板吗？',
                delete_success: '模板删除成功',
                delete_error: '模板删除失败',
                load_error: '模板加载失败'
            },
            validation: {
                name_required: '模板名称为必填项',
                name_exists: '模板名称已存在',
                category_required: '分类为必填项',
                type_required: '检测类型为必填项'
            }
        },
        detectionForm: {
            title: '检测设置',
            method: '检测方法',
            wavelength: '波长',
            time: '检测时间'
        },
        infoCard: {
            device: '设备信息',
            project: '项目信息',
            status: '状态',
            serialNumber: '序列号'
        },
        projectForm: {
            title: '项目设置',
            name: '项目名称',
            type: '项目类型',
            description: '项目描述'
        },
        ReportPara: {
            title: '报告参数',

            reportMainTitile: '报告单主标题',
            reportMainTitile_placeholder: '请输入报告单主标题',
            reportSubTitile: '报告单副标题',
            reportSubTitile_placeholder: '请输入报告单副标题',
            testMethod: '检测方法',
            testMethod_placeholder: '请输入检测方法',
            testBasis: '检测依据',
            testBasis_placeholder: '请输入检测依据',
            reviewer: '审核员',
            reviewer_placeholder: '请输入审核员姓名',

            toast: {
                success: {
                    title: '保存成功',
                    description: '已更新报告参数'
                },
                error: {
                    title: '保存失败',
                    description: '报告参数保存失败，请重试'
                }
            }
        },
        reagentSupplier: {
            title: '试剂供应商',
            placeholder: '请输入供应商名称',
            toast: {
                success: {
                    title: '保存成功',
                    description: '已更新供应商列表'
                },
                error: {
                    title: '保存失败',
                    description: '保存供应商列表时发生错误'
                }
            }
        },
        lisPara: {
            title: 'LIS参数设置',
            defaultPath: 'LIS文件默认保存路径',
            selectPath: '选择LIS保存路径',
            placeholder: '请选择LIS文件保存路径',
            toast: {
                success: {
                    title: '保存成功',
                    description: '已更新LIS文件保存路径'
                },
                error: {
                    title: '保存失败',
                    description: '保存LIS路径时发生错误'
                },
                noSelection: {
                    title: '未选择路径',
                    description: '请选择LIS文件保存路径'
                }
            }
        }
    },

    // 页面翻译
    pages: {
        home: {
            infoCard: {
                device: {
                    title: '设备管理',
                    description: '默认连接设备管理,设备连接参数配置 ...'
                },
                project: {
                    title: '项目管理',
                    description:
                        '检测项目管理（增加、修改、删除），检测参数配置、数据分析方法编辑...'
                },
                report: {
                    title: '报告管理',
                    description: '自定义检测报告，编辑打印信息，导出LIS...'
                }
            }
        },
        elisaControl: {
            title: 'ELISA 控制',
            plate_setup: {
                title: '板布局设置',
                template: {
                    label: '板布局模板',
                    new: '新建模板',
                    load: '加载模板',
                    save: '保存模板'
                },
                well_type: {
                    label: '孔类型',
                    standard: '标准品',
                    sample: '样本',
                    blank: '空白',
                    control: '质控'
                },
                concentration: {
                    label: '浓度',
                    unit: 'ng/mL'
                }
            },
            detection: {
                title: '检测控制',
                settings: {
                    wavelength: '波长',
                    temperature: '温度',
                    shaking: '振荡',
                    reading_mode: '读数模式'
                },
                progress: {
                    preparing: '准备中...',
                    detecting: '检测中...',
                    processing: '处理中...',
                    completed: '已完成',
                    remaining_time: '剩余时间：{{time}}'
                },
                actions: {
                    start: '开始检测',
                    pause: '暂停',
                    resume: '继续',
                    stop: '停止',
                    reset: '重置'
                }
            },
            results: {
                title: '实时结果',
                raw_data: '原始数据',
                processed_data: '处理数据',
                standard_curve: '标准曲线',
                export: {
                    raw: '导出原始数据',
                    processed: '导出处理数据',
                    report: '生成报告'
                }
            },
            status: {
                device: {
                    title: '设备状态',
                    temperature: '温度：{{value}}°C',
                    lamp: '光源：{{status}}',
                    door: '舱门：{{status}}'
                },
                detection: {
                    title: '检测状态',
                    current_well: '当前孔位：{{position}}',
                    progress: '进度：{{value}}%',
                    time_elapsed: '已用时间：{{time}}'
                }
            }
        },
        resultData: {
            title: '检测结果',
            filter: {
                title: '筛选结果',
                date_range: {
                    label: '日期范围',
                    start: '开始日期',
                    end: '结束日期'
                },
                project: {
                    label: '项目',
                    placeholder: '选择项目'
                },
                operator: {
                    label: '操作员',
                    placeholder: '选择操作员'
                },
                status: {
                    label: '状态',
                    all: '全部',
                    completed: '已完成',
                    processing: '处理中',
                    error: '错误'
                }
            },
            table: {
                columns: {
                    id: '编号',
                    project_name: '项目名称',
                    date: '检测日期',
                    operator: '操作员',
                    status: '状态',
                    result: '结果',
                    actions: '操作'
                },
                empty: '暂无结果',
                loading: '加载结果中...'
            },
            details: {
                title: '结果详情',
                export: '导出结果',
                print: '打印报告',
                delete: '删除结果'
            },
            chart: {
                title: '结果分析',
                concentration: '浓度',
                absorbance: '吸光度',
                standard_curve: '标准曲线',
                sample_points: '样本点'
            }
        },
        deviceManager: {
            title: '设备管理',
            connection: {
                title: '连接设置',
                port: {
                    label: '串口',
                    placeholder: '请选择串口'
                },
                baudRate: {
                    label: '波特率',
                    placeholder: '请选择波特率'
                },
                status: {
                    connected: '已连接',
                    disconnected: '未连接',
                    connecting: '连接中...',
                    error: '连接错误'
                },
                buttons: {
                    connect: '连接',
                    disconnect: '断开连接',
                    refresh: '刷新端口'
                }
            },
            control: {
                title: '设备控制',
                temperature: {
                    label: '温度控制',
                    current: '当前：{{value}}°C',
                    target: '目标：{{value}}°C',
                    set: '设置温度'
                },
                motor: {
                    label: '电机控制',
                    position: '位置：{{value}}',
                    speed: '速度：{{value}}',
                    home: '回原点',
                    move: '移动到指定位置'
                },
                lamp: {
                    label: '光源控制',
                    on: '开启',
                    off: '关闭',
                    status: '状态：{{status}}'
                }
            },
            calibration: {
                title: '设备校准',
                wavelength: {
                    label: '波长校准',
                    start: '开始校准',
                    status: '状态：{{status}}'
                },
                position: {
                    label: '位置校准',
                    start: '开始校准',
                    status: '状态：{{status}}'
                }
            },
            maintenance: {
                title: '维护',
                last_calibration: '上次校准：{{date}}',
                next_calibration: '下次校准：{{date}}',
                lamp_hours: '光源使用时间：{{hours}}',
                system_check: '系统检查',
                self_test: '自检'
            }
        },
        projectManager: {
            title: '项目管理',
            search_placeholder: '搜索  [项目名称/代码]',
            isLoadingProjects: ' 正在加载项目列表...',
            emptyProjects: '无匹配项目',
            label_deletePro: '删除项目',
            label_addPro: '新增项目',
            type: '类型',
            testWave: '检测波长',
            refWave: '参考波长',

            proPara: '项目参数',

            para: {
                title: '报告参数'
            },
            supplier: {
                title: '试剂供应商'
            },
            lis: {
                title: 'LIS参数'
            }
        },
        ReportManager: {}
    }
} as const;

export default zhCN;
