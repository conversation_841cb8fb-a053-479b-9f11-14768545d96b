<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1753768278040" clover="3.2.0">
  <project timestamp="1753768278040" name="All files">
    <metrics statements="235" coveredstatements="127" conditionals="138" coveredconditionals="60" methods="46" coveredmethods="29" elements="419" coveredelements="216" complexity="0" loc="235" ncloc="235" packages="1" files="2" classes="2"/>
    <file name="elisaFittingEngine.ts" path="E:\2-ELISA\1-1-KHB_ST_Software\software\st_elisa_project\src\renderer\src\utils\elisaFittingEngine.ts">
      <metrics statements="192" coveredstatements="124" conditionals="106" coveredconditionals="60" methods="40" coveredmethods="29"/>
      <line num="1" count="1" type="stmt"/>
      <line num="2" count="1" type="stmt"/>
      <line num="3" count="1" type="stmt"/>
      <line num="4" count="1" type="stmt"/>
      <line num="6" count="1" type="stmt"/>
      <line num="8" count="1" type="stmt"/>
      <line num="13" count="21" type="stmt"/>
      <line num="16" count="21" type="cond" truecount="2" falsecount="0"/>
      <line num="17" count="1" type="stmt"/>
      <line num="20" count="20" type="cond" truecount="2" falsecount="0"/>
      <line num="21" count="1" type="stmt"/>
      <line num="27" count="19" type="cond" truecount="5" falsecount="3"/>
      <line num="29" count="4" type="stmt"/>
      <line num="30" count="4" type="stmt"/>
      <line num="32" count="2" type="stmt"/>
      <line num="33" count="2" type="stmt"/>
      <line num="35" count="0" type="stmt"/>
      <line num="36" count="0" type="stmt"/>
      <line num="38" count="4" type="stmt"/>
      <line num="39" count="4" type="stmt"/>
      <line num="41" count="3" type="stmt"/>
      <line num="42" count="3" type="stmt"/>
      <line num="44" count="6" type="stmt"/>
      <line num="45" count="6" type="stmt"/>
      <line num="47" count="0" type="stmt"/>
      <line num="48" count="0" type="stmt"/>
      <line num="50" count="0" type="stmt"/>
      <line num="54" count="19" type="stmt"/>
      <line num="56" count="19" type="stmt"/>
      <line num="63" count="4" type="stmt"/>
      <line num="65" count="4" type="stmt"/>
      <line num="68" count="4" type="stmt"/>
      <line num="69" count="4" type="stmt"/>
      <line num="72" count="25" type="stmt"/>
      <line num="73" count="4" type="stmt"/>
      <line num="76" count="4" type="stmt"/>
      <line num="80" count="4" type="cond" truecount="1" falsecount="1"/>
      <line num="84" count="4" type="stmt"/>
      <line num="97" count="0" type="stmt"/>
      <line num="100" count="0" type="stmt"/>
      <line num="114" count="2" type="stmt"/>
      <line num="115" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="116" count="2" type="stmt"/>
      <line num="119" count="29" type="stmt"/>
      <line num="122" count="2" type="stmt"/>
      <line num="123" count="1" type="stmt"/>
      <line num="124" count="1" type="stmt"/>
      <line num="125" count="1" type="stmt"/>
      <line num="127" count="1" type="stmt"/>
      <line num="128" count="7" type="stmt"/>
      <line num="129" count="7" type="stmt"/>
      <line num="131" count="7" type="cond" truecount="2" falsecount="0"/>
      <line num="132" count="1" type="stmt"/>
      <line num="136" count="6" type="stmt"/>
      <line num="137" count="6" type="stmt"/>
      <line num="140" count="6" type="cond" truecount="1" falsecount="1"/>
      <line num="141" count="0" type="stmt"/>
      <line num="145" count="6" type="stmt"/>
      <line num="149" count="0" type="stmt"/>
      <line num="150" count="0" type="stmt"/>
      <line num="151" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="155" count="2" type="stmt"/>
      <line num="158" count="2" type="stmt"/>
      <line num="159" count="2" type="stmt"/>
      <line num="161" count="6" type="cond" truecount="2" falsecount="0"/>
      <line num="162" count="4" type="cond" truecount="3" falsecount="1"/>
      <line num="163" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="168" count="2" type="stmt"/>
      <line num="170" count="2" type="stmt"/>
      <line num="183" count="0" type="stmt"/>
      <line num="186" count="0" type="stmt"/>
      <line num="196" count="0" type="stmt"/>
      <line num="198" count="0" type="stmt"/>
      <line num="199" count="0" type="stmt"/>
      <line num="200" count="0" type="stmt"/>
      <line num="204" count="0" type="stmt"/>
      <line num="207" count="0" type="stmt"/>
      <line num="208" count="0" type="stmt"/>
      <line num="209" count="0" type="stmt"/>
      <line num="212" count="0" type="stmt"/>
      <line num="214" count="0" type="stmt"/>
      <line num="218" count="0" type="stmt"/>
      <line num="219" count="0" type="stmt"/>
      <line num="230" count="0" type="stmt"/>
      <line num="233" count="0" type="stmt"/>
      <line num="247" count="4" type="stmt"/>
      <line num="248" count="4" type="stmt"/>
      <line num="249" count="4" type="stmt"/>
      <line num="251" count="4" type="stmt"/>
      <line num="258" count="20031" type="stmt"/>
      <line num="259" count="120051" type="stmt"/>
      <line num="260" count="120051" type="stmt"/>
      <line num="263" count="4" type="stmt"/>
      <line num="264" count="4" type="stmt"/>
      <line num="265" count="4" type="stmt"/>
      <line num="267" count="27" type="stmt"/>
      <line num="268" count="4" type="stmt"/>
      <line num="269" count="1" type="cond" truecount="3" falsecount="1"/>
      <line num="270" count="1" type="stmt"/>
      <line num="273" count="4" type="stmt"/>
      <line num="275" count="4" type="stmt"/>
      <line num="288" count="0" type="stmt"/>
      <line num="291" count="0" type="stmt"/>
      <line num="305" count="3" type="stmt"/>
      <line num="308" count="3" type="stmt"/>
      <line num="309" count="3" type="stmt"/>
      <line num="310" count="3" type="stmt"/>
      <line num="312" count="3" type="stmt"/>
      <line num="319" count="18023" type="stmt"/>
      <line num="320" count="108038" type="stmt"/>
      <line num="321" count="108038" type="stmt"/>
      <line num="324" count="3" type="stmt"/>
      <line num="325" count="3" type="stmt"/>
      <line num="326" count="3" type="stmt"/>
      <line num="328" count="20" type="stmt"/>
      <line num="329" count="3" type="stmt"/>
      <line num="330" count="2" type="cond" truecount="3" falsecount="1"/>
      <line num="331" count="0" type="stmt"/>
      <line num="334" count="3" type="stmt"/>
      <line num="336" count="3" type="stmt"/>
      <line num="349" count="0" type="stmt"/>
      <line num="352" count="0" type="stmt"/>
      <line num="366" count="6" type="stmt"/>
      <line num="368" count="6" type="stmt"/>
      <line num="369" count="6" type="stmt"/>
      <line num="370" count="6" type="stmt"/>
      <line num="372" count="6" type="stmt"/>
      <line num="379" count="42059" type="stmt"/>
      <line num="380" count="336101" type="stmt"/>
      <line num="381" count="336101" type="stmt"/>
      <line num="384" count="6" type="stmt"/>
      <line num="385" count="6" type="stmt"/>
      <line num="386" count="6" type="stmt"/>
      <line num="388" count="53" type="stmt"/>
      <line num="389" count="6" type="stmt"/>
      <line num="390" count="1" type="cond" truecount="3" falsecount="1"/>
      <line num="391" count="1" type="stmt"/>
      <line num="394" count="6" type="stmt"/>
      <line num="396" count="6" type="stmt"/>
      <line num="409" count="0" type="stmt"/>
      <line num="412" count="0" type="stmt"/>
      <line num="422" count="0" type="stmt"/>
      <line num="424" count="0" type="stmt"/>
      <line num="425" count="0" type="stmt"/>
      <line num="431" count="0" type="stmt"/>
      <line num="432" count="0" type="stmt"/>
      <line num="433" count="0" type="stmt"/>
      <line num="435" count="0" type="stmt"/>
      <line num="440" count="0" type="stmt"/>
      <line num="441" count="0" type="stmt"/>
      <line num="442" count="0" type="stmt"/>
      <line num="445" count="0" type="stmt"/>
      <line num="446" count="0" type="stmt"/>
      <line num="447" count="0" type="stmt"/>
      <line num="448" count="0" type="stmt"/>
      <line num="450" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="451" count="0" type="stmt"/>
      <line num="455" count="0" type="stmt"/>
      <line num="456" count="0" type="stmt"/>
      <line num="457" count="0" type="stmt"/>
      <line num="459" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="460" count="0" type="stmt"/>
      <line num="461" count="0" type="stmt"/>
      <line num="463" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="464" count="0" type="stmt"/>
      <line num="467" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="468" count="0" type="stmt"/>
      <line num="470" count="0" type="stmt"/>
      <line num="473" count="0" type="stmt"/>
      <line num="476" count="0" type="stmt"/>
      <line num="480" count="0" type="stmt"/>
      <line num="481" count="0" type="stmt"/>
      <line num="483" count="0" type="stmt"/>
      <line num="496" count="0" type="stmt"/>
      <line num="499" count="0" type="stmt"/>
      <line num="509" count="13" type="stmt"/>
      <line num="510" count="13" type="stmt"/>
      <line num="511" count="90" type="stmt"/>
      <line num="513" count="13" type="stmt"/>
      <line num="514" count="90" type="stmt"/>
      <line num="517" count="13" type="stmt"/>
      <line num="524" count="124" type="stmt"/>
      <line num="534" count="19" type="stmt"/>
      <line num="537" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="538" count="5" type="stmt"/>
      <line num="542" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="543" count="13" type="stmt"/>
      <line num="547" count="19" type="stmt"/>
      <line num="548" count="19" type="stmt"/>
      <line num="549" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="550" count="7" type="stmt"/>
      <line num="553" count="19" type="stmt"/>
    </file>
    <file name="logger.ts" path="E:\2-ELISA\1-1-KHB_ST_Software\software\st_elisa_project\src\renderer\src\utils\logger.ts">
      <metrics statements="43" coveredstatements="3" conditionals="32" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <line num="4" count="1" type="stmt"/>
      <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="6" count="0" type="stmt"/>
      <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="16" count="0" type="stmt"/>
      <line num="18" count="0" type="stmt"/>
      <line num="19" count="0" type="stmt"/>
      <line num="22" count="0" type="stmt"/>
      <line num="29" count="0" type="stmt"/>
      <line num="32" count="1" type="stmt"/>
      <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="36" count="0" type="stmt"/>
      <line num="37" count="0" type="stmt"/>
      <line num="40" count="0" type="stmt"/>
      <line num="41" count="0" type="stmt"/>
      <line num="47" count="0" type="stmt"/>
      <line num="52" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="53" count="0" type="stmt"/>
      <line num="54" count="0" type="stmt"/>
      <line num="57" count="0" type="stmt"/>
      <line num="58" count="0" type="stmt"/>
      <line num="64" count="0" type="stmt"/>
      <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="70" count="0" type="stmt"/>
      <line num="71" count="0" type="stmt"/>
      <line num="74" count="0" type="stmt"/>
      <line num="75" count="0" type="stmt"/>
      <line num="81" count="0" type="stmt"/>
      <line num="86" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="87" count="0" type="stmt"/>
      <line num="88" count="0" type="stmt"/>
      <line num="91" count="0" type="stmt"/>
      <line num="92" count="0" type="stmt"/>
      <line num="99" count="0" type="stmt"/>
      <line num="104" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="106" count="0" type="stmt"/>
      <line num="108" count="0" type="stmt"/>
      <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="112" count="0" type="stmt"/>
      <line num="113" count="0" type="stmt"/>
      <line num="119" count="0" type="stmt"/>
      <line num="125" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
